import React from 'react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import AppRoutes from './routes';
import { AuthProvider } from './context/AuthContext';
import { ProfileProvider } from './context/ProfileContext';
import TawkToWidget from './components/common/TawkToWidget';

function App() {
  return (
    <BrowserRouter>
      <AuthProvider>
        <ProfileProvider>
          <AppRoutes />
          <TawkToWidget />
        </ProfileProvider>
      </AuthProvider>
    </BrowserRouter>
  );
}

export default App;