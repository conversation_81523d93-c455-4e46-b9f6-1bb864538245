import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';

// Extend the global Window interface to include Tawk_API
declare global {
  interface Window {
    Tawk_API?: {
      setAttributes: (attributes: { name?: string; email?: string; [key: string]: any }) => void;
      onLoad?: () => void;
      onChatMaximized?: () => void;
      onChatMinimized?: () => void;
      hideWidget?: () => void;
      showWidget?: () => void;
      [key: string]: any;
    };
  }
}

const TawkToWidget = () => {
  const { isAuthenticated, user } = useAuth();
  const location = useLocation();

  // Check if current route is a dashboard route
  const isDashboardRoute = location.pathname.startsWith('/dashboard');

  useEffect(() => {
    // Function to set user attributes when Tawk.to is ready
    const setUserAttributes = () => {
      if (window.Tawk_API && isAuthenticated && user?.email) {
        try {
          window.Tawk_API.setAttributes({
            name: user.user_metadata?.firstName && user.user_metadata?.lastName
              ? `${user.user_metadata.firstName} ${user.user_metadata.lastName}`
              : user.user_metadata?.fullName || 'User',
            email: user.email,
          });
        } catch (error) {
          console.error('Error setting Tawk.to user attributes:', error);
        }
      }
    };

    // Function to control widget visibility
    const controlWidgetVisibility = () => {
      if (window.Tawk_API) {
        try {
          // Show widget only if user is authenticated and on dashboard pages
          if (isAuthenticated && isDashboardRoute) {
            window.Tawk_API.showWidget();
            setUserAttributes();
          } else {
            window.Tawk_API.hideWidget();
          }
        } catch (error) {
          console.error('Error controlling Tawk.to widget visibility:', error);
        }
      }
    };

    // If Tawk_API is already loaded, control visibility immediately
    if (window.Tawk_API) {
      controlWidgetVisibility();
    } else {
      // Wait for Tawk.to to load
      const checkTawkLoaded = () => {
        if (window.Tawk_API) {
          controlWidgetVisibility();
        } else {
          // Check again after a short delay
          setTimeout(checkTawkLoaded, 100);
        }
      };

      // Start checking after a short delay to allow script to load
      setTimeout(checkTawkLoaded, 500);
    }

    // Set up onLoad callback for future loads
    if (window.Tawk_API) {
      const originalOnLoad = window.Tawk_API.onLoad;
      window.Tawk_API.onLoad = function() {
        if (originalOnLoad) originalOnLoad();
        controlWidgetVisibility();
      };
    }
  }, [isAuthenticated, user, isDashboardRoute]);

  // This component doesn't render anything visible
  return null;
};

export default TawkToWidget;
