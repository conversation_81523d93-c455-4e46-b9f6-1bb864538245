import React from 'react';
import { Link } from 'react-router-dom';
import { ChevronRight, Check, Box, Cpu, Shield, Zap, Server, Headphones } from 'lucide-react';
import Button from '../components/common/Button';

const Landing = () => {
  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="relative pt-24 pb-16 md:pt-32 md:pb-24 overflow-hidden">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-50 to-white -z-10"></div>
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-blue-600/5 rounded-full blur-3xl -z-10"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl lg:text-7xl font-bold text-gray-900 tracking-tight">
              <span className="text-blue-600">Deploy Applications</span><br className="hidden sm:inline" />
              in 15 Seconds!
            </h1>
            <p className="mt-6 max-w-2xl mx-auto text-xl text-gray-600">
              Sumopod is a cloud service for containers or Container as a Service. Buy, deploy, and manage containers and applications easily on a secure and scalable platform.
            </p>
            <div className="mt-10 flex flex-col sm:flex-row justify-center gap-4">
              <Button as={Link} to="/register" variant="primary" size="lg">
                Get Started
              </Button>
              <Button
                as={Link}
                to="/#features"
                variant="outline"
                size="lg"
              >
                Learn More <ChevronRight size={16} className="ml-1" />
              </Button>
            </div>

            <div className="mt-12 flex justify-center">
              <img
                src="https://images.pexels.com/photos/1181467/pexels-photo-1181467.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2"
                alt="Cloud Computing and Container Management"
                className="w-full max-w-4xl rounded-xl shadow-2xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-16 md:py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              Everything you need in one platform
            </h2>
            <p className="mt-4 max-w-2xl mx-auto text-xl text-gray-600">
              SumoPod offers comprehensive solutions for container and application management
            </p>
            <div className="mt-8">
              <Button
                as={Link}
                to="/#pricing"
                variant="outline"
                size="md"
              >
                View Pricing
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Feature 1 */}
            <div className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-shadow border border-gray-100">
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mb-5">
                <Box className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Container Marketplace</h3>
              <p className="text-gray-600">
                Explore and purchase from our extensive container library, all verified and ready for instant deployment.
              </p>
            </div>

            {/* Feature 2 */}
            <div className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-shadow border border-gray-100">
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mb-5">
                <Server className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">One-Click Deployment</h3>
              <p className="text-gray-600">
                Deploy containers to your infrastructure with one click, eliminating complex configuration processes.
              </p>
            </div>

            {/* Feature 3 */}
            <div className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-shadow border border-gray-100">
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mb-5">
                <Cpu className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Resource Optimization</h3>
              <p className="text-gray-600">
                Advanced metrics and monitoring help you optimize resource allocation and reduce infrastructure costs.
              </p>
            </div>

            {/* Feature 4 */}
            <div className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-shadow border border-gray-100">
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mb-5">
                <Shield className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Enterprise Security</h3>
              <p className="text-gray-600">
                Bank-level security with encryption, access controls, and compliance features to protect your data.
              </p>
            </div>

            {/* Feature 5 */}
            <div className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-shadow border border-gray-100">
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mb-5">
                <Zap className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Automatic Updates</h3>
              <p className="text-gray-600">
                Keep your containers and applications up to date with automatic version updates and security patches.
              </p>
            </div>

            {/* Feature 6 */}
            <div className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-shadow border border-gray-100">
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mb-5">
                <Headphones className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">24/7 Expert Support</h3>
              <p className="text-gray-600">
                Our expert team is available around the clock to help you with technical issues or any questions.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-16 md:py-24 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

          <div className="mt-12 max-w-lg mx-auto">
            <div className="bg-white rounded-xl shadow-lg overflow-hidden border-2 border-blue-500">
              <div className="p-8 text-center">
                <h3 className="text-2xl font-bold text-gray-900">Start Today</h3>
                <div className="mt-4 flex items-baseline justify-center">
                  <span className="text-5xl font-extrabold text-blue-600">FREE</span>
                </div>
                <p className="mt-4 text-gray-600">
                  All the features you need to manage containers and applications effectively
                </p>
                <div className="mt-8 space-y-3">
                  <Button
                    as={Link}
                    to="/register"
                    variant="primary"
                    size="lg"
                    className="w-full"
                  >
                    Get Started
                  </Button>
                  <Button
                    as={Link}
                    to="/#features"
                    variant="outline"
                    size="lg"
                    className="w-full"
                  >
                    Learn More
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 md:py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-blue-600 rounded-2xl overflow-hidden shadow-xl">
            <div className="px-6 py-12 md:p-12 text-center md:text-left md:flex md:items-center md:justify-between">
              <div>
                <h2 className="text-2xl md:text-3xl font-bold text-white">
                  Ready to transform your container management?
                </h2>
                <p className="mt-4 text-lg text-blue-100 max-w-2xl">
                  Join thousands of businesses using SumoPod to simplify their container and application infrastructure.
                </p>
              </div>
              <div className="mt-8 md:mt-0 flex flex-col md:flex-row gap-4">
                <Button as={Link} to="/register" variant="secondary" size="lg" className="md:w-auto font-semibold">
                  Get Started
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Landing;